import React, { useState, useEffect, useCallback, useMemo } from 'react';
import Image from 'next/image';
import { FiMenu, FiX, FiChevronDown } from 'react-icons/fi';
import Link from 'next/link';
import { useRouter, usePathname } from 'next/navigation';
import { useDispatch, useSelector } from 'react-redux';
import { logout } from '@/store/features/authSlice';
import { Icon } from '@iconify/react';
import { NotificationBell, NotificationPanel } from './notifications';

const Header = () => {
  const { isAuth, user } = useSelector((state) => state.auth);
  const router = useRouter();
  const pathname = usePathname();
  const dispatch = useDispatch();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeMenu, setActiveMenu] = useState(null);
  const [showNotification, setShowNotification] = useState(false);
  const activePlan = user?.activePlan;

  // Function to check if a submenu item is active based on the current path
  const isSubmenuItemActive = useCallback(
    (path) => {
      return pathname === path || pathname.startsWith(path + '/');
    },
    [pathname]
  );

  const profileImage = '';
  // console.log(profileImage);

  const getMenuItemsByPlan = (plan) => {
    const baseMenuItems = [
      {
        name: 'Introduction',
        path: '/',
      },
      {
        name: 'HEC Diary',
        path: '/diary',
        submenu: [
          { name: 'My Diary', path: '/diary/my' },
          { name: 'Mission Diary', path: '/diary-missions' },
          { name: 'Award', path: '/diary/award' },
          { name: 'Find Friends', path: '/find-friends' },
          { name: 'My Tutor', path: '/tutors?planFeatureType=hec_user_diary' },
          // { name: 'The Hall of the Fame', path: '/diary/hall-of-fame' },
        ],
      },
      {
        name: 'HEC Play',
        path: '/play',
        submenu: [
          { name: 'Block', path: '/block' },
          { name: 'Waterfall', path: '/waterfall' },
          // { name: 'Award', path: '/play/award' },
          { name: 'Story Maker', path: '/story-maker' },
          { name: 'My Tutor', path: '/tutors?planFeatureType=hec_play' },
          // { name: 'The Hall of the Fame', path: '/play/hall-of-fame' },
        ],
      },
    ];

    // Define additional menus based on subscription plan
    const additionalMenus = {
      'Standard Monthly': [
        {
          name: 'HEC Q&A',
          path: '/question-answer',
          submenu: [
            { name: 'My HEC Q&A', path: '/question-answer' },
            { name: 'Mission Q&A', path: '/hec-mission' },
            { name: 'My Tutor', path: '/tutors' },
          ],
        },
      ],
      'Pro Monthly': [
        {
          name: 'HEC Q&A',
          path: '/question-answer',
          submenu: [
            { name: 'My HEC Q&A', path: '/question-answer' },
            { name: 'Mission Q&A', path: '/hec-mission' },
            { name: 'My Tutor', path: '/tutors' },
          ],
        },
        {
          name: 'HEC Essay',
          path: '/essay',
          submenu: [
            { name: 'My HEC Essay', path: '/essay' },
            { name: 'Mission Essay', path: '/essay/mission' },
            { name: 'Award', path: '/essay/awards' },
            { name: 'My Tutor', path: '/tutors?planFeatureType=english_essay' },
            // { name: 'The Hall of the Fame', path: '/essay/hall-of-fame' },
          ],
        },
      ],
      'Ultimate Monthly': [
        {
          name: 'HEC Q&A',
          path: '/question-answer',
          submenu: [
            { name: 'My HEC Q&A', path: '/question-answer' },
            { name: 'Mission Q&A', path: '/hec-mission' },
            { name: 'My Tutor', path: '/tutors' },
          ],
        },
        {
          name: 'HEC Essay',
          path: '/essay',
          submenu: [
            { name: 'My HEC Essay', path: '/essay' },
            { name: 'Mission Essay', path: '/essay/mission' },
            { name: 'Award', path: '/essay/awards' },
            { name: 'My Tutor', path: '/tutors?planFeatureType=english_essay' },
            // { name: 'The Hall of the Fame', path: '/essay/hall-of-fame' },
          ],
        },
        {
          name: 'HEC Novel',
          path: '/novel',
          submenu: [
            // { name: 'My HEC Novel', path: '/novel/my' },
            // { name: 'Open Projects', path: '/novel/open-projects' },
            // { name: 'Award', path: '/novel/award' },
            { name: 'My Tutor', path: '/tutors?planFeatureType=english_novel' },
            // { name: 'The Hall of the Fame', path: '/novel/hall-of-fame' },
          ],
        },
      ],
    };

    if (!plan) return baseMenuItems;
    return [...baseMenuItems, ...(additionalMenus[plan] || [])];
  };

  const menuItems = useMemo(() => {
    return isAuth && activePlan
      ? getMenuItemsByPlan(activePlan)
      : [
          {
            name: 'Introduction',
            path: '/',
          },
          {
            name: 'About Us',
            path: '/aboutus',
          },
          {
            name: 'Contact Us',
            path: '/contactus',
          },
        ];
  }, [isAuth, activePlan]);

  // We're removing the auto-opening behavior
  // This effect is intentionally removed to prevent menus from opening automatically

  useEffect(() => {
    const handleOutsideClick = (event) => {
      // Close active menu when clicking outside
      if (
        activeMenu &&
        !event.target.closest('.menu-item') &&
        !event.target.closest('.submenu')
      ) {
        setActiveMenu(null);
      }

      // Close notification dropdown when clicking outside
      if (
        showNotification &&
        !event.target.closest('.notification-bell') &&
        !event.target.closest('.notification-dropdown')
      ) {
        setShowNotification(false);
      }
    };

    document.addEventListener('click', handleOutsideClick);
    return () => {
      document.removeEventListener('click', handleOutsideClick);
    };
  }, [activeMenu, showNotification]);

  const handleMenuClick = (menuName) => {
    if (activeMenu === menuName) {
      setActiveMenu(null);
    } else {
      setActiveMenu(menuName);
    }
  };

  const handleMenuItemClick = (path) => {
    setActiveMenu(null);
    router.push(path);
  };

  return (
    <div>
      <div className="fixed top-0 left-0 w-full z-50 shadow-md">
        <header className="bg-[#FFF189] text-black relative">
          <div className="flex justify-between items-center max-w-7xl mx-auto px-5 xl:px-0 relative">
            <Link href="/">
              <Image
                src="/assets/images/all-img/Logo.png"
                alt="icon"
                width={120}
                height={80}
                priority
                className="cursor-pointer max-w-24"
              />
            </Link>

            <button
              className="lg:hidden text-black text-2xl"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              aria-label="Toggle Menu"
            >
              {isMenuOpen ? <FiX /> : <FiMenu />}
            </button>

            <div className="hidden lg:flex items-center space-x-4 relative">
              {isAuth && (
                <>
                  <div className="relative">
                    <NotificationBell />
                    <NotificationPanel />
                  </div>
                  <Link href="/chat">
                    <Icon
                      icon="icon-park-solid:communication"
                      width="28"
                      height="28"
                      className="cursor-pointer notification-bell text-gray-500"
                    />
                  </Link>
                </>
              )}
              {isAuth ? (
                <div className="group relative">
                  <div className="flex items-center gap-2 py-2">
                    <Image
                      src={
                        profileImage
                          ? profileImage
                          : '/assets/images/all-img/avatar.png'
                      }
                      alt="icon"
                      width={80}
                      height={80}
                      className="w-10 h-10 rounded-full border border-white"
                    />

                    <p>{user?.name?.split(' ')[0]}</p>

                    <Icon
                      icon={'mingcute:down-line'}
                      width="16"
                      height="16"
                      className="group-hover:rotate-180 transition-transform duration-300 opacity-70"
                    />
                  </div>

                  <div className="hidden group-hover:block absolute top-10 left-0 mt-4 w-40 bg-white border border-gray-200 rounded-md shadow-lg z-50 p-2">
                    <Link href="/dashboard">
                      <button className="w-full px-4 py-2 hover:bg-[#FFFAC2] rounded-lg text-start">
                        Dashboard
                      </button>
                    </Link>

                    <Link href="/profile">
                      <button className="w-full px-4 py-2 hover:bg-[#FFFAC2] rounded-lg text-start">
                        Profile
                      </button>
                    </Link>

                    <button
                      onClick={() => (dispatch(logout()), router.push('/'))}
                      className="w-full px-4 py-2 hover:bg-red-50 rounded-lg  text-start"
                    >
                      Logout
                    </button>
                  </div>
                </div>
              ) : (
                <>
                  <Link
                    href="/register"
                    className="px-6 py-2 rounded-md bg-black text-white border border-gray-600"
                  >
                    Join
                  </Link>
                  <Link href="/login">
                    <button className="px-6 py-2 bg-[#FFFAC2] rounded-md border border-gray-600 ">
                      Login
                    </button>
                  </Link>
                </>
              )}{' '}
            </div>
          </div>
        </header>

        {isMenuOpen && (
          <div className="md:hidden absolute top-[52px] left-0 w-full bg-[#FFFAC2] shadow-md p-5 z-50">
            {/* Mobile language switcher removed */}

            {/* Mobile Menu Items */}
            <div className="mb-4 border-b border-gray-300 ">
              {menuItems.map((item, index) => (
                <div key={index} className="mb-2">
                  <button
                    className={`w-full text-left py-2 rounded-md menu-item flex justify-between items-center ${
                      activeMenu === item.name
                        ? 'bg-[#FFE34D] font-bold'
                        : 'bg-[#FFFAC2]'
                    } hover:bg-[#FFE34D]`}
                    onClick={() => handleMenuClick(item.name)}
                  >
                    <span>{item.name}</span>
                    {item.submenu && (
                      <FiChevronDown
                        className={`transition-transform duration-200 ${
                          activeMenu === item.name ? 'transform rotate-180' : ''
                        }`}
                        size={16}
                      />
                    )}
                  </button>

                  {item.submenu && activeMenu === item.name && (
                    <>
                      {/* Small triangle indicator */}
                      <div className="absolute top-0 left-[-5px] transform -translate-y-1/2 w-2 h-2 bg-white border-t border-l border-[#FFE34D] rotate-45 z-50 ml-4"></div>
                      <div className="ml-4 mt-1 border-l-2 border-[#FFE34D] pl-2 submenu bg-white rounded-md relative z-40">
                        {item.submenu.map((subItem, subIndex) => (
                          <div key={subIndex}>
                            <Link
                              href={subItem.path}
                              className={`block px-4 py-2 text-sm ${
                                isSubmenuItemActive(subItem.path)
                                  ? 'font-bold text-yellow-600'
                                  : ''
                              } hover:font-bold rounded-md mt-1 transition-all duration-200 transform hover:scale-105`}
                              onClick={() => handleMenuItemClick(subItem.path)}
                            >
                              {subItem.name}
                            </Link>
                            {subIndex < item.submenu.length - 1 && (
                              <div className="mx-2 border-b border-dashed border-gray-300"></div>
                            )}
                          </div>
                        ))}
                      </div>
                    </>
                  )}
                </div>
              ))}
            </div>

            <div className="flex items-center space-x-4 ">
              {isAuth ? (
                <div className="group relative">
                  <div className="flex items-center gap-2 py-2">
                    <Image
                      src={
                        profileImage
                          ? profileImage
                          : '/assets/images/all-img/avatar.png'
                      }
                      alt="icon"
                      width={80}
                      height={80}
                      className="w-10 h-10 rounded-full border border-white"
                    />

                    <p>{user?.name?.split(' ')[0]}</p>

                    <Icon
                      icon={'mingcute:down-line'}
                      width="16"
                      height="16"
                      className="group-hover:rotate-180 transition-transform duration-300 opacity-70"
                    />
                  </div>

                  <div className="hidden group-hover:block absolute top-10 left-0 mt-4 w-40 bg-white border border-gray-200 rounded-md shadow-lg z-50 p-2">
                    <Link
                      href="/dashboard"
                      className="w-full inline-block px-4 py-2 hover:bg-[#FFFAC2] rounded-lg text-start"
                    >
                      Dashboard
                    </Link>

                    <Link
                      href="/profile"
                      className="w-full inline-block px-4 py-2 hover:bg-[#FFFAC2] rounded-lg text-start"
                    >
                      Profile
                    </Link>

                    <button
                      onClick={() => (dispatch(logout()), router.push('/'))}
                      className="w-full px-4 py-2 hover:bg-red-50 rounded-lg text-start"
                    >
                      Logout
                    </button>
                  </div>
                </div>
              ) : (
                <>
                  <Link
                    href="/register"
                    className="px-6 py-2 rounded-md bg-black text-white border border-gray-600"
                  >
                    Join
                  </Link>
                  <Link href="/login">
                    <button className="px-6 py-2 bg-[#FFFAC2] rounded-md border border-gray-600 ">
                      Login
                    </button>
                  </Link>
                </>
              )}
            </div>
          </div>
        )}

        {/* Main Menu Bar */}
        <div className="py-3 text-center bg-[#FFDE34] relative hidden lg:block">
          <div className="flex items-center justify-center space-x-8">
            {menuItems.map((item, index) => (
              <div key={index} className="relative group">
                <Link
                  href={item.path}
                  className={`relative text-black hover:text-black menu-item flex items-center ${
                    activeMenu === item.name
                      ? 'font-bold'
                      : ''
                  }`}
                  onClick={(e) => {
                    if (item.submenu) {
                      e.preventDefault();
                      handleMenuClick(item.name);
                    }
                  }}
                >
                  {item.name}
                  {item.submenu && (
                    <FiChevronDown
                      className={`ml-1 transition-transform duration-200 ${
                        activeMenu === item.name ? 'transform rotate-180' : ''
                      }`}
                      size={16}
                    />
                  )}
                  <span
                    className={`absolute bottom-[-10px] left-0 w-full h-[4px] bg-[#cd7f32] transform ${
                      activeMenu === item.name ? 'scale-x-100' : 'scale-x-0'
                    } group-hover:scale-x-100 transition-all duration-500 origin-right z-10`}
                  ></span>
                </Link>

                {/* Submenu */}
                {item.submenu && activeMenu === item.name && (
                  <>
                    {/* Triangle/arrow above the submenu */}
                    <div className="absolute left-1/2 transform -translate-x-1/2 mt-4 w-4 h-4 bg-white border-t border-l border-gray-200 rotate-45 z-50"></div>
                    <div className="absolute left-1/2 transform -translate-x-1/2 mt-6 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-40 overflow-hidden submenu">
                      {item.submenu.map((subItem, subIndex) => (
                        <React.Fragment key={subIndex}>
                          <Link
                            href={subItem.path}
                            className={`block px-4 py-2 text-sm ${
                              isSubmenuItemActive(subItem.path)
                                ? 'font-bold text-yellow-600'
                                : 'text-gray-700'
                            } hover:font-bold transition-all duration-200 transform hover:scale-105`}
                            onClick={() => handleMenuItemClick(subItem.path)}
                          >
                            {subItem.name}
                          </Link>
                          {subIndex < item.submenu.length - 1 && (
                            <div className="mx-2 border-b border-dashed border-gray-300"></div>
                          )}
                        </React.Fragment>
                      ))}
                    </div>
                  </>
                )}
              </div>
            ))}

            {!activePlan && <div className='absolute right-28'>
              <Link href="/pricing-plans" className="text-white bg-gray-800 border border-gray-100 rounded-lg text-sm px-4 py-1.5">
                Subscribe Now
              </Link>
            </div>}
          </div>
        </div>
      </div>
      <div className="h-[52px] lg:h-[102px]"></div>
    </div>
  );
};

export default Header;
