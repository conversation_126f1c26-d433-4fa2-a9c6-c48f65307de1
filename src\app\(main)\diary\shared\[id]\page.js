'use client';
import React, { useEffect, useState, useRef } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Image from 'next/image';
import useDataFetch from '@/hooks/useDataFetch';
import Canvas from '@/components/skin/Canvas';
import SkinPreview from '@/components/skin/SkinPreview';

const SharedDiaryDetails = () => {
  const { id } = useParams();
  const router = useRouter();
  

  // Fetch diary entry details
  const { data: diaryEntry, isLoading, error } = useDataFetch({
    queryKey: ['shared-diary-details', id],
    endPoint: `/diary/entries/${id}`,
    enabled: !!id,
  });

  // Format the date for better display
  const formatDate = (dateString) => {
    const options = { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      weekday: 'long'
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Handle loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#432005] mx-auto mb-4"></div>
          <p className="text-lg text-[#432005]">Loading diary entry...</p>
        </div>
      </div>
    );
  }

  // Handle error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-lg text-red-600 mb-4">Error loading diary entry</p>
          <button 
            onClick={() => router.back()}
            className="bg-[#432005] text-white px-6 py-2 rounded-lg hover:bg-[#5a2a07] transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  // Handle case where diary entry is not found
  if (!diaryEntry) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-lg text-gray-600 mb-4">Diary entry not found</p>
          <button 
            onClick={() => router.back()}
            className="bg-[#432005] text-white px-6 py-2 rounded-lg hover:bg-[#5a2a07] transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#FFF8F0] to-[#F5E6D3]">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Header Section */}
        <div className="mb-8">
          <button 
            onClick={() => router.back()}
            className="flex items-center text-[#432005] hover:text-[#5a2a07] transition-colors mb-4"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Back to Shared Diaries
          </button>
          
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h1 className="text-3xl font-bold text-[#432005]">{diaryEntry.title}</h1>
              <span className="text-sm text-gray-500">
                {formatDate(diaryEntry.entryDate || diaryEntry.createdAt)}
              </span>
            </div>
            
            {/* Author Info */}
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-200">
                <Image
                  src={diaryEntry.user?.profilePicture || "/api/placeholder/48/48"}
                  alt={diaryEntry.user?.name || "Author"}
                  width={48}
                  height={48}
                  className="w-full h-full object-cover"
                />
              </div>
              <div>
                <p className="font-semibold text-[#432005]">
                  {diaryEntry.user?.name || "Anonymous"}
                </p>
                <p className="text-sm text-gray-600">Diary Author</p>
              </div>
            </div>
            
            {/* Likes and interactions */}
            <div className="flex items-center space-x-4 pt-4 border-t border-gray-200">
              <div className="flex items-center space-x-1">
                <span className="text-2xl">👍</span>
                <span className="text-lg font-bold text-[#432005]">
                  {diaryEntry.likesCount || 0}
                </span>
                <span className="text-sm text-gray-600">likes</span>
              </div>
              <div className="flex items-center space-x-1">
                <span className="text-2xl">💬</span>
                <span className="text-lg font-bold text-[#432005]">
                  {diaryEntry.commentsCount || 0}
                </span>
                <span className="text-sm text-gray-600">comments</span>
              </div>
            </div>
          </div>
        </div>

        {/* Diary Content */}
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="p-6">
            <h2 className="text-xl font-semibold text-[#432005] mb-4">Diary Content</h2>
            
            <div className="">
              <SkinPreview 
                skin={diaryEntry.skin?.templateContent}
                contentData={{
                  subject: diaryEntry.title,
                  body: diaryEntry.content,
                  date: diaryEntry.entryDate,
                }}
              />
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="mt-8 flex justify-center space-x-4">
          <button className="bg-gradient-to-br from-[#FFF8E1] to-[#F5E6A3] px-6 py-3 rounded-full font-semibold text-[#432005] border border-[#E6D16A] hover:shadow-lg transition-all duration-200 flex items-center space-x-2">
            <span className="text-lg">👍</span>
            <span>Like</span>
          </button>
          
          <button className="bg-gradient-to-br from-[#FFF8E1] to-[#F5E6A3] px-6 py-3 rounded-full font-semibold text-[#432005] border border-[#E6D16A] hover:shadow-lg transition-all duration-200 flex items-center space-x-2">
            <span className="text-lg">💬</span>
            <span>Comment</span>
          </button>
          
          <button className="bg-gradient-to-br from-[#FFF8E1] to-[#F5E6A3] px-6 py-3 rounded-full font-semibold text-[#432005] border border-[#E6D16A] hover:shadow-lg transition-all duration-200 flex items-center space-x-2">
            <span className="text-lg">📤</span>
            <span>Share</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default SharedDiaryDetails;
