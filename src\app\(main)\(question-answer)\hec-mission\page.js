'use client';
import DetailsModal from '@/components/form/modal/MissionConfirmationModal';
import useDataFetch from '@/hooks/useDataFetch';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react';
import MissionDetailsModal from '../../essay/mission/_components/MissionDetailsModal';

const HekMission = () => {
  const router = useRouter();
  const [weeklyMission, setWeeklyMission] = useState(true);
  const [selectedMission, setSelectedMission] = useState(null);

  const { data: activeTask } = useDataFetch({
    queryKey: ['active-task'],
    endPoint: `/student-qa-mission/activeTask`,
  });

  const { data, isLoading } = useDataFetch({
    queryKey: ['hec-mission', weeklyMission],
    endPoint: `/student-qa-mission/getMissionList${
      weeklyMission ? '?timeFrequency=weekly' : '?timeFrequency=monthly'
    }`,
  });

  // Get missions directly from the API response
  const missions = data?.items || [];

  if (activeTask?.task) {
    router.push(`/question-answer?itemId=${activeTask?.id}`);
    return null;
  }

  return (
    <div className="">
      <div className="max-w-7xl mx-auto px-5 xl:px-0 relative z-10 my-10 space-y-4">
        <div className="rounded-xl shadow-lg border p-5 bg-[#FFF9FB]">
          <div>
            <div className="flex items-start justify-between text-gray-600">
              <div>
                <h1 className="text-2xl text-yellow-800 font-semibold">
                  Hello English Coaching Q & A
                </h1>
                {/* <p>Instruction:</p>
                <p>1. Write a short writtings on a dog.</p> */}
              </div>

              <h1 className="bg-gradient-to-b from-[#ECB306] to-[#AE6E33] bg-clip-text text-3xl font-extrabold text-transparent font-serif">
                HEC Q & A
              </h1>
            </div>

            {/* <button className="flex items-center gap-2 border border-yellow-800 text-3xl text-yellow-900 bg-gradient-to-b from-[#DCA600] to-[#FFDE5B] px-10 py-1 rounded-full mt-3">
              GO{' '}
              <ButtonIcon
                icon={'tabler:arrow-right'}
                innerBtnCls={'h-8 w-8 '}
                btnIconCls={'h-4 w-4'}
              />
            </button> */}
          </div>
        </div>

        <div className="p-5 bg-[#FFFDF5] shadow-md border rounded-lg">
          <div className="">
            <div className="flex items-center border-2 border-yellow-500 rounded-full bg-[#FEFCE8]">
              <div className="w-full flex-1 relative">
                <button
                  onClick={() => setWeeklyMission(true)}
                  className={`w-full flex-1 py-2.5 ${
                    weeklyMission && 'bg-yellow-500  rounded-l-full'
                  }`}
                >
                  Weekly Mission
                </button>
                {weeklyMission && (
                  <div className="h-6 w-6 bg-yellow-500 rotate-45 absolute z-0 -bottom-3 left-1/2 -translate-x-1/2"></div>
                )}
              </div>

              <div className="w-full flex-1 relative">
                <button
                  onClick={() => setWeeklyMission(false)}
                  className={`w-full py-2.5 ${
                    !weeklyMission && 'bg-yellow-500 rounded-r-full'
                  }`}
                >
                  Monthly Mission
                </button>

                {!weeklyMission && (
                  <div className="h-6 w-6 bg-yellow-500 rotate-45 absolute z-0 -bottom-3 left-1/2 -translate-x-1/2"></div>
                )}
              </div>
            </div>
          </div>

          <div className="">
            {/* Loading indicator */}
            {isLoading ? (
              <div className="flex justify-center items-center py-20">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
              </div>
            ) : (
              /* Display missions directly from API response */
              missions.map((mission) => (
                <div
                  key={mission.id}
                  className="py-5 max-w-[377px] mx-auto space-y-8 mb-10"
                >
                  <div className="flex justify-center pb-3">
                    <button className="bg-gradient-to-b from-[#FFDE5B] to-[#DCA600] min-w-60 py-2 text-2xl rounded-lg">
                      {weeklyMission
                        ? `Week ${mission.sequenceNumber}`
                        : `Month ${mission.sequenceNumber}`}
                    </button>
                  </div>

                  {/* Map through each task in the mission */}
                  {mission.tasks.map((task, taskIdx) => {
                    // Find the corresponding progress for this task
                    const taskProgressItem = mission.taskProgress.find(
                      (progress) => progress.id === task.id
                    );
                    const taskProgress = taskProgressItem?.progress || 0;
                    const progressPercent = `${taskProgress}%`;

                    // Create a unique index for this task
                    const idx = taskIdx;

                    return (
                      <div key={task.id}>
                        <div className="relative w-full">
                          <div
                            className={`flex items-center gap-2 ${
                              idx % 2 == 0
                                ? 'justify-start'
                                : 'justify-start flex-row-reverse'
                            } w-full`}
                          >
                            <div
                              onClick={() => setSelectedMission(task)}
                              className="h-14 w-14 rounded-full cursor-pointer relative"
                              style={{
                                background: `conic-gradient(${
                                  taskProgress === 100 ? '#22c55e' : '#92400e'
                                } ${taskProgress * 3.6}deg, #FFDE34 ${
                                  taskProgress * 3.6
                                }deg)`,
                              }}
                            >
                              <div className="absolute top-1 left-1 right-1 bottom-1 bg-white rounded-full flex items-center justify-center">
                                <div
                                  className="h-12 w-12 rounded-full flex items-center justify-center text-xs font-semibold"
                                  style={{
                                    backgroundColor:
                                      taskProgress === 100
                                        ? '#bbf7d0'
                                        : '#fefce8',
                                    color:
                                      taskProgress === 100
                                        ? '#15803d'
                                        : '#92400e',
                                  }}
                                >
                                  {progressPercent}
                                </div>
                              </div>
                            </div>

                            <div>
                              <p className="font-semibold">{task.title}</p>
                              {taskProgress === 100 && (
                                <p className="">Complete</p>
                              )}
                            </div>
                          </div>

                          {/* Only show connecting line if not the last task */}
                          {!(taskIdx === mission.tasks.length - 1) && (
                            <Image
                              src={
                                idx % 2 == 0
                                  ? '/assets/images/all-img/mission-shape2.png'
                                  : '/assets/images/all-img/mission-shape1.png'
                              }
                              alt={'arrow'}
                              width={280}
                              height={20}
                              className={`absolute max-w-[70%] sm:w-full left-1/2 -translate-x-1/2 ${
                                idx % 2 == 0 ? '-bottom-5' : '-bottom-6'
                              }`}
                            />
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              ))
            )}

            {/* Show message if no missions available */}
            {!isLoading && missions.length === 0 && (
              <div className="py-10 text-center">
                <p className="text-gray-500">
                  No missions available for this time period.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {!!selectedMission && (
        <MissionDetailsModal
          isOpen={!!selectedMission}
          onClose={() => setSelectedMission(null)}
          data={selectedMission}
          title={'Mission Essay'}
          endPoint={'/student-qa-mission/start/task'}
          returnTo={`/question-answer?itemId=${selectedMission?.id}`}
          topic="Essay"
        />
      )}
    </div>
  );
};

export default HekMission;
