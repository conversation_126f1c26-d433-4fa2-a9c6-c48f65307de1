'use client';

import { useState } from 'react';
import { useDispatch } from 'react-redux';
import { useSearchParams } from 'next/navigation';
import SkinPreview from '@/components/skin/SkinPreview';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import DetailsModal from '@/components/form/modal/MissionConfirmationModal';
import { ButtonIcon } from '@/components/Button';
import EssayFeedBackModal from './_components/FeedbackModal';

export default function WriteEssay() {
  const dispatch = useDispatch();
  const taskId = useSearchParams().get('taskId');
  const [isSaving, setIsSaving] = useState(false);
  const [modalData, setModalData] = useState(null);

  // State for subject, body, and date
  const [subject, setSubject] = useState('my custom');
  const [body, setBody] = useState('my custom body content');
  const [date, setDate] = useState(() => new Date().toISOString().slice(0, 10)); // YYYY-MM-DD

  const {
    data: skinInfo,
    isLoading,
    refetch,
  } = useDataFetch({
    queryKey: ['essay-skin-info', taskId],
    endPoint: `/student-essay/skins/${taskId}`,
    enabled: !!taskId,
  });

  const {
    data: activeTask,
  } = useDataFetch({
    queryKey: ['/student-essay/activeTask'],
    endPoint: `/student-essay/activeTask`,
  });

  const handleSave = async () => {
    setIsSaving(true);

    const payload = {
      taskId: taskId,
      // skinId: essayId,
      title: subject,
      content: body,
    };

    try {
      const response = await api.post('/student-essay/submit/task', payload);
      refetch();
    } catch (error) {
      console.error('Error saving:', error);
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) return <div>Loading...</div>;

  return (
    <div className="max-w-7xl mx-auto p-5 xl:px-0 my-8">
      <div className="p-2 bg-[#FDE7E9] flex flex-col lg:flex-row items-center gap-3">
        <div className="flex-1 bg-white rounded-lg h-full space-y-5 text-center p-5 w-full">
          <SkinPreview
            skin={skinInfo?.moduleDefaultSkin.skin.templateContent}
            contentData={{
              subject,
              body,
              date,
            }}
          />

          <button
            onClick={handleSave}
            disabled={isSaving}
            className={`  text-black font-medium py-2 px-8  text-center rounded-full whitespace-nowrap
            border-2 border-yellow-100
            shadow-[0px_4px_6px_-1px_rgba(0,0,0,0.1),0px_2px_4px_-2px_rgba(0,0,0,0.1),-4px_8px_12px_0px_#00000026]
            transition-all duration-300
            bg-gradient-to-b from-yellow-300 to-yellow-500 hover:from-yellow-400 hover:to-yellow-600
            relative pr-8
            ring-2 ring-[#A36105] ${
              isSaving
                ? 'bg-gray-300 cursor-not-allowed'
                : 'bg-yellow-400 hover:bg-yellow-300'
            }`}
          >
            {isSaving ? 'Saving...' : 'Submit'}
          </button>
        </div>

        {/* Example inputs for subject, body, and date */}
        <div className="flex-1 bg-white rounded-lg p-3 space-y-3 w-full">
          <div className="flex flex-col p-2 shadow-lg border rounded-lg gap-3">
            <div className="flex items-center justify-between border-b border-dashed pb-1 gap-3">
              <input
                type="text"
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
                placeholder="Subject"
                className="p-2 mr-2 w-full focus:outline-[1px] focus:outline-gray-200"
              />
              <input
                type="date"
                value={date}
                onChange={(e) => setDate(e.target.value)}
                className="border p-2 mr-2 rounded-lg"
              />
            </div>
            <textarea
              value={body}
              onChange={(e) => setBody(e.target.value)}
              placeholder="Body"
              className="border p-2 w-full rounded focus:outline-[1px] focus:outline-gray-400 mb-4 shadow-[inset_2px_2px_6px_0px_#0000001F]"
              rows={4}
            />
          </div>

          <div className="h-full shadow-lg border rounded-lg p-2 relative">
            <div className="h-full">
              <p className="text-sm text-[#864D0D] text-center font-medium mb-2">
                Tutor Review Zone
              </p>
              <div className="flex justify-between items-center border-b-2 border-dashed border-gray-300 mb-3">
                <h3 className="mb-2">{subject}</h3>
                <div className="flex items-center gap-3 text-sm">{date}</div>
              </div>
              <div
                className="min-h-28"
                dangerouslySetInnerHTML={{
                  __html: body || '',
                }}
              />
              {/* {todayEntry?.status === 'confirm' && (
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                  <h1 className="text-lg text-[#14AE5C] font-medium">
                    Confirmed
                  </h1>
                </div>
              )} */}
            </div>

            <span className="absolute right-2 bottom-2">
              <ButtonIcon
                icon="tabler:message-2-star"
                innerBtnCls="h-12 w-12"
                btnIconCls="h-5 w-5"
                onClick={() => setModalData( body)}
                aria-label=""
                withbackground={false}
              />
            </span>
          </div>
        </div>
      </div>

      <EssayFeedBackModal
        isOpen={!!modalData}
        onClose={() => setModalData(null)}
        data={modalData}
      />
    </div>
  );
}
